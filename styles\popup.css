/* Base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

body {

  line-height: 1.5;
  color: #333;
  width: 350px;
  margin: 0;
  padding: 0;
  background-color: #ffffff;
  color: #202124;
    border-radius: 0.5rem;
}

.hidden {
  display: none !important;
}

.icon {
  display: inline-block;
  vertical-align: middle;
}

/* App container */
.app-container {
  display: flex;
  width: 100%;
  min-height: 100vh;
  justify-content: center;
  align-items: center;
  background-color: #f3f4f6;
}

/* Open translator button */
.open-translator-btn {
  padding: 0.5rem 1rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.open-translator-btn:hover {
  background-color: #2563eb;
}

/* Translate popup */
.container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  z-index: 50;
}

/*
This media query is causing problems in the extension popup.
Let's modify it to use a different approach.
*/

/* Mobile styles will be applied via JavaScript detection */
.container.mobile-view {
  position: fixed;
  bottom: 0;
  right: 0;
  width: 100%;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Extension popup styles */
.container.extension-popup {
  width: auto;
  min-width: 350px;
  max-width: 450px;
}

/* Simple mode for page translation only */
.container.simple-mode .tabs,
.container.simple-mode .model-selector-container,
.container.simple-mode #textTranslationMode,
.container.simple-mode #aiQuestionSection,
.container.simple-mode #apiKeySection {
  display: none !important;
}

.container.simple-mode #pageTranslationMode {
  display: block !important;
  margin-top: 10px;
}

/* Minimized state */
.container.minimized {
  width: 300px;
}

/* Maximized state */
.container.maximized {
  width: 500px;
}

/* Popup header */
.popup-header {
  background-color: #2563eb;
  color: white;
  padding: 0.625rem 0.75rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@media (min-width: 768px) {
  .popup-header {
    padding: 0.75rem 1rem;
  }
}

.header-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.popup-title {
  font-weight: 500;
  font-size: 0.875rem;
}

@media (min-width: 768px) {
  .popup-title {
    font-size: 1rem;
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

@media (min-width: 768px) {
  .header-actions {
    gap: 0.5rem;
  }
}

.header-btn {
  padding: 0.25rem;
  border-radius: 0.25rem;
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
}

.header-btn:hover {
  background-color: #1d4ed8;
}

/* Toggle page translation button styles */
#togglePageTranslation {
  transition: all 0.2s ease;
}

#togglePageTranslation:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.simple-mode #togglePageTranslation {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Translate page button */
.translate-page-btn {
  width: 100%;
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 0.25rem;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

@media (min-width: 768px) {
  .translate-page-btn {
    font-size: 0.875rem;
  }
}

.translate-page-btn:hover {
  background-color: #2563eb;
}

/* Tabs */
.tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
}

.tab-btn {
  flex: 1;
  padding: 0.375rem 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  background: transparent;
  border: none;
  color: #6b7280;
  cursor: pointer;
}

@media (min-width: 768px) {
  .tab-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

.tab-btn:hover {
  color: #374151;
}

.tab-btn.active {
  color: #2563eb;
  border-bottom: 2px solid #2563eb;
}

/* Popup content */
.popup-content {
  padding: 0.75rem;
}

@media (min-width: 768px) {
  .popup-content {
    padding: 1rem;
  }
}

/* Model selector */
.model-selector-container {
  margin-bottom: 0.75rem;
}

.model-label {
  display: block;
  font-size: 0.75rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

@media (min-width: 768px) {
  .model-label {
    font-size: 0.875rem;
  }
}

.select-container {
  position: relative;
}

.model-select {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  padding-right: 2.5rem;
  font-size: 0.875rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  appearance: none;
  background-color: white;
}

.model-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

@media (min-width: 768px) {
  .model-select {
    padding: 0.5rem 0.75rem;
  }
}

.select-arrow {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  padding: 0 0.5rem;
  pointer-events: none;
  color: #6b7280;
}

/* Language selector */
.language-selector {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

@media (min-width: 768px) {
  .language-selector {
    justify-content: space-between;
    gap: 0.5rem;
  }
}

.lang-select {
  font-size: 0.75rem;
  border: none;
  background-color: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  color: #4b5563;
  max-width: 120px;
  flex: 1;
}

@media (min-width: 768px) {
  .lang-select {
    font-size: 0.875rem;
    flex: none;
    max-width: none;
  }
}

.lang-select:focus {
  outline: none;
}

/* Tone selector */
.tone-selector {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  background-color: #f9fafb;
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
}

.tone-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  white-space: nowrap;
}

.tone-select {
  font-size: 0.75rem;
  border: none;
  background-color: transparent;
  color: #4b5563;
  flex: 1;
  padding: 0.25rem;
}

.tone-select:focus {
  outline: none;
}

@media (min-width: 768px) {
  .tone-label, .tone-select {
    font-size: 0.875rem;
  }
}

.switch-btn {
  padding: 0.375rem;
  border-radius: 9999px;
  border: none;
  background-color: transparent;
  cursor: pointer;
  margin: 0 0.25rem;
}

@media (min-width: 768px) {
  .switch-btn {
    margin: 0 0.5rem;
  }
}

.switch-btn:hover {
  background-color: #f3f4f6;
}

.switch-arrows {
  display: flex;
  flex-direction: column;
  align-items: center;
}

@media (min-width: 768px) {
  .switch-arrows {
    flex-direction: row;
  }
}

/* Page translation mode */
.page-translation-mode {
  padding: 0.75rem 1rem;
  background-color: #eff6ff;
  border-radius: 0.5rem;
  border: 1px solid #bfdbfe;
}

.page-translation-text {
  font-size: 0.75rem;
  color: #1e40af;
  margin-bottom: 0.75rem;
}

@media (min-width: 768px) {
  .page-translation-text {
    font-size: 0.875rem;
  }
}

/* Persistent translation option */
.persistent-translation-option {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  font-size: 0.75rem;
  color: #1e40af;
  position: relative;
}

.persistent-checkbox {
  margin-right: 0.5rem;
  cursor: pointer;
}

.tooltip {
  position: relative;
  display: inline-block;
  margin-left: 0.5rem;
  cursor: help;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: 200px;
  background-color: #333;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 0.7rem;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

.translate-page-action {
  width: 100%;
  padding: 0.5rem;
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.translate-page-action:hover {
  background-color: #1d4ed8;
}

/* Text translation mode */
.textarea-container {
  margin-bottom: 0.75rem;
}

.source-textarea,
.translated-textarea {
  width: 100%;
  height: 5rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  resize: none;
}

@media (min-width: 768px) {
  .source-textarea,
  .translated-textarea {
    height: 6rem;
    padding: 0.75rem;
  }
}

.source-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.translated-textarea {
  background-color: #f9fafb;
}

.textarea-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 0.25rem;
}

/* Text area with button */
.textarea-with-button {
  position: relative;
  width: 100%;
}

.copy-btn-inside, .clear-text {
  position: absolute;
  top: 5px;
  right: 5px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 2px;
  border-radius: 3px;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.copy-btn-inside:hover, .clear-text:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.1);
}

.material-icons {
  font-size: 18px;
  color: #555;
}

/* Ask AI section styles */
.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.ai-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.clear-conversation-btn {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  color: #6b7280;
}

.clear-conversation-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.conversation-container {
  margin-bottom: 0.75rem;
}

.conversation-box {
  height: 200px;
  overflow-y: auto;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.75rem;
  background-color: #f9fafb;
  margin-bottom: 0.75rem;
}

.empty-conversation {
  color: #6b7280;
  text-align: center;
  padding: 2rem 0;
}

.message {
  margin-bottom: 0.75rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  max-width: 85%;
}

.user-message {
  background-color: #e5e7eb;
  color: #111827;
  align-self: flex-end;
  margin-left: auto;
}

.ai-message {
  background-color: #dbeafe;
  color: #1e40af;
  align-self: flex-start;
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  align-self: flex-start;
}

.question-form {
  display: flex;
  gap: 0.5rem;
}

.question-input {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.question-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.send-btn {
  padding: 0.5rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-btn:hover:not(:disabled) {
  background-color: #2563eb;
}

.send-btn:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

/* Loading indicator */
.loading-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.25rem;
}

.dot {
  width: 8px;
  height: 8px;
  background-color: #1e40af;
  border-radius: 50%;
  animation: pulse 1.5s infinite ease-in-out;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* Code formatting in AI responses */
.code-block {
  background-color: #f3f4f6;
  border-radius: 0.25rem;
  padding: 0.5rem;
  margin: 0.5rem 0;
  overflow-x: auto;
  font-family: monospace;
  white-space: pre;
}

.inline-code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: monospace;
}

.voice-btn {
  color: #2563eb;
  padding: 0.25rem;
  border-radius: 0.25rem;
  border: none;
  background-color: transparent;
  cursor: pointer;
}

.voice-btn:hover {
  background-color: #f3f4f6;
}

/* Styles for textarea with button inside */
.textarea-with-button {
  position: relative;
  width: 100%;
}

.textarea-with-button .translated-textarea {
  width: 100%;
  padding-right: 40px; /* Make room for the button */
}

.copy-btn-inside {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 4px;
  padding: 4px;
  cursor: pointer;
  color: #2563eb;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s, color 0.2s;
}

.copy-btn-inside:hover {
  background-color: #2563eb;
  color: white;
}

/* AI Question Section */
.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 0 0.25rem;
}

.ai-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.conversation-header h4 {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin: 0;
}

.clear-conversation-btn {
  background: transparent;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
}

.clear-conversation-btn:hover {
  background-color: #f3f4f6;
  color: #ef4444;
}

.conversation-container {
  margin-bottom: 0.5rem;
}

.conversation-box {
  height: 250px;
  overflow-y: auto;
  margin-bottom: 0.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  background-color: #f9fafb;
}

@media (min-width: 768px) {
  .conversation-box {
    height: 300px;
    margin-bottom: 0.75rem;
    padding: 0.75rem;
  }
}

.empty-conversation {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 0.875rem;
}

.message {
  padding: 0.5rem;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
}

.user-message {
  background-color: #dbeafe;
  margin-left: 1rem;
}

@media (min-width: 768px) {
  .user-message {
    margin-left: 1.5rem;
  }
}

.ai-message {
  background-color: white;
  border: 1px solid #e5e7eb;
  margin-right: 1rem;
  position: relative;
}

.ai-message:hover::after {
  content: "📋";
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  font-size: 0.75rem;
  cursor: pointer;
  opacity: 0.5;
}

.ai-message:hover::after:hover {
  opacity: 1;
}

.copy-notification {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  z-index: 10;
}

@media (min-width: 768px) {
  .ai-message {
    margin-right: 1.5rem;
  }
}

.message-sender {
  font-size: 0.6875rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

@media (min-width: 768px) {
  .message-sender {
    font-size: 0.75rem;
  }
}

.message-content {
  font-size: 0.75rem;
}

@media (min-width: 768px) {
  .message-content {
    font-size: 0.875rem;
  }
}

.thinking {
  text-align: center;
  font-size: 0.75rem;
  color: #6b7280;
}

@media (min-width: 768px) {
  .thinking {
    font-size: 0.875rem;
  }
}

.question-form {
  display: flex;
  gap: 0.5rem;
}

.question-input {
  flex: 1;
  padding: 0.5rem;
  font-size: 0.875rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
}

.question-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.send-btn {
  background-color: #2563eb;
  color: white;
  padding: 0.5rem;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
}

.send-btn:hover:not(:disabled) {
  background-color: #1d4ed8;
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* API Key Section */
.api-key-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.api-key-container {
  padding: 0.75rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.api-key-label {
  display: block;
  font-size: 0.75rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

@media (min-width: 768px) {
  .api-key-label {
    font-size: 0.875rem;
  }
}

.api-key-input-container {
  position: relative;
  margin-bottom: 0.75rem;
}

.api-key-input {
  width: 100%;
  padding: 0.5rem 2.5rem 0.5rem 0.75rem;
  font-size: 0.875rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
}

.api-key-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.toggle-visibility-btn {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
}

.toggle-visibility-btn:hover {
  color: #374151;
}

.api-key-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.save-api-key-btn {
  padding: 0.375rem 0.75rem;
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

@media (min-width: 768px) {
  .save-api-key-btn {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }
}

.save-api-key-btn:hover {
  background-color: #1d4ed8;
}

.get-api-key-link {
  font-size: 0.75rem;
  color: #2563eb;
  text-decoration: none;
}

@media (min-width: 768px) {
  .get-api-key-link {
    font-size: 0.875rem;
  }
}

.get-api-key-link:hover {
  text-decoration: underline;
}

.api-key-status {
  font-size: 0.75rem;
  margin-top: 0.5rem;
  min-height: 1.5rem;
}

.api-key-status.success {
  color: #059669;
}

.api-key-status.error {
  color: #dc2626;
}
.status{
  font-size: 13px;
  color: green;
  text-align: center;
}
.status.error{
  font-size: 13px;
  color: red;
  text-align: center;
}

/* Settings Section */
.settings-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

/* Video Translate Section */
.video-translate-header {
  margin-bottom: 1rem;
}

.video-translate-header h4 {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin: 0;
}

.video-translate-options {
  margin-bottom: 1rem;
}

.option-group {
  margin-bottom: 0.75rem;
}

.toggle-option {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.toggle-checkbox {
  margin-right: 0.5rem;
}

.toggle-label {
  font-size: 0.75rem;
  color: #4b5563;
}

.language-selector {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.language-label {
  font-size: 0.75rem;
  margin-right: 0.5rem;
  flex-shrink: 0;
  color: #4b5563;
  min-width: 70px;
}

.video-translate-info {
  background-color: #f3f4f6;
  border-radius: 0.375rem;
  padding: 0.75rem;
}

.info-note {
  display: flex;
  align-items: flex-start;
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.75rem;
}

.note-icon {
  margin-right: 0.375rem;
  font-size: 0.875rem;
}

.save-settings-btn {
  width: 100%;
  padding: 0.5rem;
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  cursor: pointer;
}

.save-settings-btn:hover {
  background-color: #1d4ed8;
}

/* YouTube Subtitles Section (in settings) */
.youtube-subtitles-section {
  margin-top: 1rem;
}

.youtube-subtitles-container {
  padding: 0.75rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.youtube-subtitles-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.youtube-subtitles-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin: 0;
}

.youtube-subtitles-toggle {
  display: flex;
  align-items: center;
}

.youtube-subtitles-options {
  margin-top: 0.75rem;
}

.subtitle-display-mode {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.subtitle-mode-label {
  font-size: 0.75rem;
  margin-right: 0.5rem;
  flex-shrink: 0;
  color: #4b5563;
}

.subtitle-mode-select {
  flex-grow: 1;
  padding: 0.375rem 0.5rem;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
  background-color: white;
  color: #4b5563;
  font-size: 0.75rem;
}

/* شخصی‌سازی زیرنویس */
.subtitle-customization-section {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

/* YouTube Subtitle Translation Section */
.youtube-subtitle-section {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
}

.step-section {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: #ffffff;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
}

.step-section:last-child {
  margin-bottom: 0;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  background-color: #3b82f6;
  color: white;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
}

.step-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
}

.step-description {
  font-size: 0.75rem;
  color: #64748b;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
}

.info-note {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 0.375rem;
  margin-bottom: 0.75rem;
}

.info-icon {
  font-size: 0.875rem;
}

.info-text {
  font-size: 0.75rem;
  color: #1e40af;
  font-weight: 500;
}

.section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.section-description {
  font-size: 0.75rem;
  color: #64748b;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  justify-content: center;
}

.download-btn {
  background-color: #059669;
  color: white;
}

.download-btn:hover:not(:disabled) {
  background-color: #047857;
}

.upload-btn {
  background-color: #3b82f6;
  color: white;
}

.upload-btn:hover:not(:disabled) {
  background-color: #2563eb;
}



.action-btn:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
  opacity: 0.6;
}

.btn-icon {
  font-size: 0.875rem;
}

/* File Upload Styling */
.file-upload-area {
  margin-bottom: 0.75rem;
}

.file-input {
  display: none;
}

.file-upload-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border: 2px dashed #cbd5e1;
  border-radius: 0.375rem;
  background-color: #f8fafc;
  cursor: pointer;
  transition: all 0.2s ease;
  justify-content: center;
}

.file-upload-label:hover {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.upload-icon {
  font-size: 1rem;
}

.upload-text {
  font-size: 0.75rem;
  font-weight: 500;
  color: #475569;
}

.selected-file-name {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #059669;
  font-weight: 500;
  text-align: center;
}

.clear-file-btn {
  margin-top: 0.5rem;
  padding: 0.25rem 0.5rem;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: 0.7rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  justify-content: center;
  width: 100%;
  transition: background-color 0.2s ease;
}

.clear-file-btn:hover {
  background-color: #dc2626;
}

.clear-file-btn {
  margin-top: 0.5rem;
  padding: 0.25rem 0.5rem;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: 0.7rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  justify-content: center;
  width: 100%;
  transition: background-color 0.2s ease;
}

.clear-file-btn:hover {
  background-color: #dc2626;
}

.status-message {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  text-align: center;
  padding: 0.5rem;
  border-radius: 0.25rem;
  display: none;
}

.status-message.success {
  background-color: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
  display: block;
}

.status-message.error {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
  display: block;
}

.status-message.info {
  background-color: #eff6ff;
  color: #2563eb;
  border: 1px solid #bfdbfe;
  display: block;
}

.customization-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin: 0 0 0.75rem 0;
  text-align: center;
}

.customization-option {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 0.75rem;
}

.customization-option label {
  font-size: 0.75rem;
  margin-right: 0.5rem;
  color: #4b5563;
  min-width: 70px;
}

.slider {
  flex: 1;
  height: 5px;
  border-radius: 5px;
  background: #d1d5db;
  outline: none;
  margin: 0 0.5rem;
}

.value-display {
  font-size: 0.75rem;
  color: #4b5563;
  min-width: 40px;
  text-align: right;
}

input[type="color"] {
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 4px;
  padding: 0;
  cursor: pointer;
}

.subtitle-note {
  display: flex;
  align-items: flex-start;
  font-size: 0.75rem;
  color: #6b7280;
}

.subtitle-note .note-icon {
  margin-right: 0.375rem;
  font-size: 0.875rem;
}

/* Keyboard Shortcuts Section */
.keyboard-shortcuts-section {
  margin-top: 1rem;
}

.shortcuts-container {
  padding: 0.75rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.shortcuts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.shortcuts-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin: 0;
}

.shortcuts-toggle {
  display: flex;
  align-items: center;
}

.toggle-checkbox {
  margin-right: 0.5rem;
}

.toggle-label {
  font-size: 0.75rem;
  color: #4b5563;
}

.shortcut-item {
  margin-bottom: 0.5rem;
}

.shortcut-description {
  font-size: 0.75rem;
  color: #4b5563;
  margin-bottom: 0.5rem;
}

.shortcut-key-container {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.shortcut-key-combo {
  display: flex;
  align-items: center;
  flex: 1;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.375rem 0.5rem;
}

.modifier-checkbox {
  display: flex;
  align-items: center;
  margin-right: 0.5rem;
  font-size: 0.75rem;
  color: #4b5563;
  cursor: pointer;
}

.modifier-checkbox input {
  margin-right: 0.25rem;
}

.plus-sign {
  margin: 0 0.25rem;
  color: #6b7280;
  font-weight: bold;
}

.key-input {
  width: 1.5rem;
  text-align: center;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  padding: 0.125rem;
  font-size: 0.75rem;
  text-transform: uppercase;
}

.save-shortcut-btn {
  padding: 0.375rem 0.75rem;
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  margin-left: 0.5rem;
  cursor: pointer;
}

.save-shortcut-btn:hover {
  background-color: #1d4ed8;
}

.shortcut-status {
  font-size: 0.75rem;
  color: #059669;
  min-height: 1.25rem;
}

.shortcut-status.error {
  color: #dc2626;
}

.shortcut-note {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

.note-icon {
  margin-right: 0.375rem;
  font-size: 0.875rem;
}

/* Only hide keyboard shortcuts section on actual mobile devices */
/* This is now handled by JavaScript detection based on user agent */

/* Translation status indicator */
.translation-status {
  padding: 0.5rem;
  margin-bottom: 0.75rem;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #6b7280;
  text-align: center;
}

.translation-status.loading {
  background-color: #fef3c7;
  border-color: #f59e0b;
  color: #92400e;
}

.translation-status.success {
  background-color: #d1fae5;
  border-color: #10b981;
  color: #065f46;
}

.translation-status.error {
  background-color: #fee2e2;
  border-color: #ef4444;
  color: #991b1b;
}

/* Troubleshooting tip */
.troubleshooting-tip {
  padding: 0.75rem;
  margin-top: 0.75rem;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #374151;
}

.troubleshooting-tip p {
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.troubleshooting-tip ul {
  margin: 0;
  padding-left: 1.25rem;
}

.troubleshooting-tip li {
  margin-bottom: 0.25rem;
}