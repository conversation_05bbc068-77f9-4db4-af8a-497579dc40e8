// Video Translate tab functionality
document.addEventListener("DOMContentLoaded", function() {
  // Get elements
  const videoTargetLang = document.getElementById("videoTargetLang");
  const videoTranslateTone = document.getElementById("videoTranslateTone");
  const enableVideoTranslate = document.getElementById("enableVideoTranslate");
  const saveVideoTranslateSettings = document.getElementById("saveVideoTranslateSettings");

  // YouTube subtitle elements
  const openDownSub = document.getElementById("openDownSub");
  const subtitleFileInput = document.getElementById("subtitleFileInput");
  const selectedFileName = document.getElementById("selectedFileName");
  const subtitleStatus = document.getElementById("subtitleStatus");
  const testSubtitleDisplay = document.getElementById("testSubtitleDisplay");
  
  // شخصی‌سازی زیرنویس
  const subtitleFontSize = document.getElementById("subtitle-font-size");
  const subtitleBgOpacity = document.getElementById("subtitle-bg-opacity");
  const subtitleOpacity = document.getElementById("subtitle-opacity");
  
  // نمایش مقادیر
  const fontSizeValue = document.getElementById("font-size-value");
  const bgOpacityValue = document.getElementById("bg-opacity-value");
  const opacityValue = document.getElementById("opacity-value");
  
  
  
  // بروزرسانی نمایش مقادیر
  if (subtitleFontSize && fontSizeValue) {
    subtitleFontSize.addEventListener("input", () => {
      fontSizeValue.textContent = `${subtitleFontSize.value}px`;
    });
  }
  
  if (subtitleBgOpacity && bgOpacityValue) {
    subtitleBgOpacity.addEventListener("input", () => {
      bgOpacityValue.textContent = `${subtitleBgOpacity.value}%`;
    });
  }
  
  if (subtitleOpacity && opacityValue) {
    subtitleOpacity.addEventListener("input", () => {
      opacityValue.textContent = `${subtitleOpacity.value}%`;
    });
  }
  
  // Load video translate settings
  browser.storage.local.get([
    "youtubeSubtitlesEnabled",
    "lastTargetLang",
    "videoTranslateTone",
    "subtitleFontSize",
    "subtitleOpacity"
  ]).then(result => {
    // Set video target language to match the main target language
    if (result.lastTargetLang) {
      videoTargetLang.value = result.lastTargetLang;
    }
    
    // Set tone if saved
    if (result.videoTranslateTone) {
      videoTranslateTone.value = result.videoTranslateTone;
    }
    
    // Set enabled state
    enableVideoTranslate.checked = result.youtubeSubtitlesEnabled !== false;
    console.log(enableVideoTranslate)
    // Set display mode
   
    
    // تنظیم مقادیر شخصی‌سازی زیرنویس
    if (result.subtitleFontSize && subtitleFontSize) {
      const fontSize = parseInt(result.subtitleFontSize);
      subtitleFontSize.value = fontSize;
      if (fontSizeValue) fontSizeValue.textContent = `${fontSize}px`;
    }
    
    if (result.subtitleOpacity && subtitleOpacity) {
      const opacity = parseFloat(result.subtitleOpacity) * 100;
      subtitleOpacity.value = opacity;
      if (opacityValue) opacityValue.textContent = `${opacity}%`;
    }
  });
  
  // Save video translate settings
  saveVideoTranslateSettings.addEventListener("click", () => {

    // تنظیمات شخصی‌سازی زیرنویس
    const fontSize = subtitleFontSize ? `${subtitleFontSize.value}px` : "16px";
    const bgOpacity = subtitleBgOpacity ? subtitleBgOpacity.value / 100 : 0.8;
    const opacity = subtitleOpacity ? subtitleOpacity.value / 100 : 1;
    
    const settings = {
      youtubeSubtitlesEnabled: enableVideoTranslate.checked,
      lastTargetLang: videoTargetLang.value,
      videoTranslateTone: videoTranslateTone.value,
      subtitleFontSize: fontSize,
      subtitleOpacity: opacity.toString()
    };
    
    browser.storage.local.set(settings).then(() => {
      // Show success message
      const notification = document.createElement("div");
      notification.textContent = "Video translation settings saved!";
      notification.style.backgroundColor = "#059669";
      notification.style.color = "white";
      notification.style.padding = "8px 12px";
      notification.style.borderRadius = "4px";
      notification.style.marginTop = "10px";
      notification.style.textAlign = "center";
      
      const infoSection = document.querySelector(".video-translate-info");
      infoSection.appendChild(notification);
      
      setTimeout(() => {
        notification.remove();
      }, 3000);
      
      // Send message to content script to update settings
      browser.tabs.query({ active: true, currentWindow: true }).then(tabs => {
        if (tabs && tabs.length > 0) {
          browser.tabs.sendMessage(tabs[0].id, {
            action: "updateYouTubeSubtitlesSettings",
            enabled: settings.youtubeSubtitlesEnabled,
            targetLanguage: settings.lastTargetLang,
            tone: settings.videoTranslateTone,
          }).catch(error => {
            console.log("Error updating YouTube subtitles settings:", error);
          });
          
          // ارسال تنظیمات شخصی‌سازی زیرنویس
          browser.tabs.sendMessage(tabs[0].id, {
            action: "updateSubtitleCustomization",
            fontSize: settings.subtitleFontSize,
            bgColor: settings.subtitleBgColor,
            textColor: settings.subtitleTextColor,
            opacity: settings.subtitleOpacity
          }).catch(error => {
            console.log("Error updating subtitle customization:", error);
          });
        }
      });
    });
  });
  
  // Update main target language when video target language changes
  videoTargetLang.addEventListener("change", () => {
    browser.storage.local.set({ lastTargetLang: videoTargetLang.value });
    
    // Also update the main target language dropdown if it exists
    const mainTargetLang = document.getElementById("targetLang");
    if (mainTargetLang) {
      mainTargetLang.value = videoTargetLang.value;
    }
  });

  // Handle open DownSub button
  if (openDownSub) {
    openDownSub.addEventListener("click", function() {
      // Get current YouTube video URL
      browser.tabs.query({ active: true, currentWindow: true }).then(tabs => {
        if (tabs && tabs.length > 0) {
          const currentUrl = tabs[0].url;

          if (currentUrl.includes("youtube.com/watch")) {
            // Open DownSub with the current YouTube URL
            const downSubUrl = `https://downsub.com/?url=${encodeURIComponent(currentUrl)}`;
            browser.tabs.create({ url: downSubUrl });
          } else {
            // Open DownSub homepage
            browser.tabs.create({ url: "https://downsub.com/" });
            showStatus(subtitleStatus, "Please navigate to a YouTube video first for direct link", "info");
          }
        }
      }).catch(error => {
        console.error("Error opening DownSub:", error);
        // Fallback: just open DownSub homepage
        browser.tabs.create({ url: "https://downsub.com/" });
      });
    });
  }

  // Handle subtitle file selection - Auto process when file is selected
  if (subtitleFileInput) {
    subtitleFileInput.addEventListener("change", async function(event) {
      const file = event.target.files[0];
      if (!file) {
        selectedFileName.textContent = "";
        hideStatus(subtitleStatus);
        return;
      }

      // Validate file type
      if (!file.name.toLowerCase().endsWith('.srt')) {
        showStatus(subtitleStatus, "Please select a valid SRT file", "error");
        subtitleFileInput.value = "";
        return;
      }

      selectedFileName.textContent = `Selected: ${file.name}`;
      showStatus(subtitleStatus, "Processing and translating subtitles...", "info");

      try {
        // Get active tab
        const tabs = await browser.tabs.query({ active: true, currentWindow: true });
        if (!tabs || tabs.length === 0) {
          throw new Error("No active tab found");
        }

        const tab = tabs[0];

        // Check if we're on YouTube
        if (!tab.url.includes("youtube.com/watch")) {
          throw new Error("Please navigate to a YouTube video page");
        }

        // Read file content
        const fileContent = await readFileAsText(file);

        // Get current settings
        const targetLanguage = videoTargetLang.value;
        const tone = videoTranslateTone.value;

        // Send message to content script to process subtitles
        console.log("🚀 Sending processCustomSubtitles message to tab:", tab.id);
        console.log("📦 Message data:", {
          action: "processCustomSubtitles",
          fileName: file.name,
          targetLanguage: targetLanguage,
          tone: tone,
          fileContentLength: fileContent ? fileContent.length : 0
        });

        const response = await browser.tabs.sendMessage(tab.id, {
          action: "processCustomSubtitles",
          fileContent: fileContent,
          fileName: file.name,
          targetLanguage: targetLanguage,
          tone: tone
        });

        console.log("📨 Received response:", response);

        if (response && response.success) {
          showStatus(subtitleStatus, "✅ Subtitles translated successfully! Watch your video to see them.", "success");

          // Close popup after successful processing
          setTimeout(() => {
            window.close();
          }, 3000);
        } else {
          throw new Error(response?.message || "Failed to process subtitle file");
        }

      } catch (error) {
        console.error("❌ Error processing subtitles:", error);

        // Check if it's a connection error
        if (error.message.includes("Could not establish connection")) {
          showStatus(subtitleStatus, "❌ Could not connect to YouTube page. Please refresh the page and try again.", "error");
        } else if (error.message.includes("No tab found")) {
          showStatus(subtitleStatus, "❌ No active tab found. Please make sure you're on a YouTube page.", "error");
        } else {
          showStatus(subtitleStatus, "❌ " + error.message, "error");
        }

        // Reset file input on error
        subtitleFileInput.value = "";
        selectedFileName.textContent = "";
      }
    });
  }

  // Handle test subtitle display button
  if (testSubtitleDisplay) {
    testSubtitleDisplay.addEventListener("click", async function() {
      try {
        // Get active tab
        const tabs = await browser.tabs.query({ active: true, currentWindow: true });
        if (!tabs || tabs.length === 0) {
          throw new Error("No active tab found");
        }

        const tab = tabs[0];

        // Check if we're on YouTube
        if (!tab.url.includes("youtube.com")) {
          showStatus(subtitleStatus, "Please navigate to a YouTube page first", "error");
          return;
        }

        // Send test message to content script
        const response = await browser.tabs.sendMessage(tab.id, {
          action: "testSubtitleDisplay"
        });

        if (response && response.success) {
          showStatus(subtitleStatus, "Test subtitle should be visible on the page!", "success");
        } else {
          showStatus(subtitleStatus, "Test failed: " + (response?.message || "Unknown error"), "error");
        }

      } catch (error) {
        console.error("Error testing subtitle display:", error);
        showStatus(subtitleStatus, "Test failed: " + error.message, "error");
      }
    });
  }

  // Helper function to show status messages
  function showStatus(element, message, type) {
    if (!element) return;

    element.textContent = message;
    element.className = `status-message ${type}`;
    element.style.display = "block";

    // Auto-hide success messages after 5 seconds
    if (type === "success") {
      setTimeout(() => {
        hideStatus(element);
      }, 5000);
    }
  }

  // Helper function to hide status messages
  function hideStatus(element) {
    if (!element) return;
    element.style.display = "none";
  }

  // Helper function to read file as text
  function readFileAsText(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = () => reject(new Error("Failed to read file"));
      reader.readAsText(file, 'utf-8');
    });
  }

});