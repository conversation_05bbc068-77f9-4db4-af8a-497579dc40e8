// YouTube subtitles content script loaded
console.log("YouTube subtitles content script loaded successfully!");

// YouTube subtitles content script loaded
console.log("🎬 YouTube subtitles content script loaded successfully!");

// Test if we're on YouTube
if (window.location.hostname.includes('youtube.com')) {
  console.log("✅ We are on YouTube domain");
} else {
  console.log("❌ Not on YouTube domain:", window.location.hostname);
}

try {
  // Add message listener
  browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log("🔔 YouTube subtitles script received message:", message);

    if (message.action === "processCustomSubtitles") {
      console.log("🎯 Processing custom subtitles...");
      const { fileContent, fileName, targetLanguage, tone } = message;

      try {
        // Log received data
        console.log("📁 File name:", fileName);
        console.log("🌍 Target language:", targetLanguage);
        console.log("🎭 Tone:", tone);
        console.log("📄 File content length:", fileContent ? fileContent.length : 0);

        // TODO: Implement subtitle processing logic here

        // Send success response
        sendResponse({
          success: true,
          message: "Subtitles processed successfully!",
        });

        console.log("✅ Response sent successfully");

      } catch (error) {
        console.error("❌ Error processing subtitles:", error);
        sendResponse({
          success: false,
          message: "Failed to process subtitles: " + error.message,
        });
      }
    } else if (message.action === "testSubtitleDisplay") {
      console.log("🧪 Testing subtitle display...");

      // Create a test subtitle
      const testSubtitle = document.createElement('div');
      testSubtitle.id = 'youtube-subtitle-test';
      testSubtitle.style.cssText = `
        position: fixed;
        bottom: 100px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        font-size: 18px;
        z-index: 9999;
        font-family: Arial, sans-serif;
      `;
      testSubtitle.textContent = 'Test subtitle - Extension is working! 🎉';

      document.body.appendChild(testSubtitle);

      // Remove after 3 seconds
      setTimeout(() => {
        if (testSubtitle.parentNode) {
          testSubtitle.parentNode.removeChild(testSubtitle);
        }
      }, 3000);

      sendResponse({
        success: true,
        message: "Test subtitle displayed"
      });
    }

    return true; // Keep the message channel open for async response
  });

  console.log("🎧 Message listener added successfully");

} catch (error) {
  console.error("❌ Error setting up YouTube subtitles message listener:", error);
}
