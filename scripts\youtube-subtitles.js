// YouTube subtitles content script loaded
console.log("YouTube subtitles content script loaded successfully!");

try {
  browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log("YouTube subtitles script received message:", message);

    if (message.action === "processCustomSubtitles") {
      console.log("Processing custom subtitles...");
      const { fileContent, fileName, targetLanguage, tone } = message;

      try {
        // TODO: Implement subtitle processing logic here
        console.log("File content:", fileContent);
        console.log("Target language:", targetLanguage);
        console.log("Tone:", tone);

        // ارسال پاسخ موفقیت‌آمیز
        sendResponse({
          success: true,
          message: "Subtitles processed successfully!",
        });
      } catch (error) {
        console.error("Error processing subtitles:", error);
        sendResponse({
          success: false,
          message: "Failed to process subtitles: " + error.message,
        });
      }
    }

    return true; // Keep the message channel open for async response
  });
} catch (error) {
  console.error("Error setting up YouTube subtitles message listener:", error);
}
