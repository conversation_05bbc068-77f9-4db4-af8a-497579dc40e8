/* Styles for Gemini Selection Translator */

/* Notification styles */
.gemini-translator-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #2563eb;
  color: white;
  padding: 12px 15px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 280px;
  max-width: 350px;
  animation: slideIn 0.3s ease-out forwards;
  opacity: 1;
  transform: translateX(0);
  transition: opacity 0.5s, transform 0.5s;
}

.gemini-translator-notification.hiding {
  opacity: 0;
  transform: translateX(20px);
}

.gemini-translator-notification-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.gemini-translator-notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.gemini-translator-notification-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 18px;
  padding: 0;
  margin-left: 10px;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.gemini-translator-notification-close:hover {
  opacity: 1;
}

@keyframes slideIn {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Selection translator popup styles */
#gemini-selection-translator {
  position: absolute;
  z-index: 10000;
  background-color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
  padding: 15px;
  max-width: 350px;
  display: none;
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  border: 1px solid #e5e7eb;
  animation: fadeIn 0.2s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

#gemini-selection-translator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

#gemini-translation-title {
  font-weight: 600;
  color: #2563eb;
  display: flex;
  align-items: center;
  gap: 6px;
}

#gemini-translation-close {
  cursor: pointer;
  font-weight: bold;
  font-size: 18px;
  color: #6b7280;
  transition: color 0.2s;
}

#gemini-translation-close:hover {
  color: #374151;
}

#gemini-translation-loading {
  font-style: italic;
  color: #6b7280;
  margin-bottom: 8px;
  display: none;
}

#gemini-translation-content {
  min-height: 20px;
  line-height: 1.5;
  color: #374151;
  margin-bottom: 10px;
}

#gemini-translation-copy {
  background-color: #f3f4f6;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  color: #4b5563;
  cursor: pointer;
  transition: background-color 0.2s;
  display: none;
}

#gemini-translation-copy:hover {
  background-color: #e5e7eb;
  color: #374151;
}

/* Context menu styles */
.gemini-context-menu {
  position: absolute;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  border-radius: 6px;
  z-index: 10001;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.gemini-context-menu-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
  transition: background-color 0.2s;
}

.gemini-context-menu-item:hover {
  background-color: #f3f4f6;
}
